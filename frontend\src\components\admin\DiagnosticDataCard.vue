<template>
  <NCard title="Diagnostic Information" class="diagnostic-card">
    <template #header-extra>
      <NTag v-if="diagnosticData" type="success" size="small">
        Enhanced Report
      </NTag>
      <NTag v-else type="warning" size="small">
        Legacy Report
      </NTag>
    </template>

    <div v-if="!diagnosticData" class="no-diagnostic-data">
      <NResult 
        status="info"
        title="No Diagnostic Data"
        description="This report was submitted before diagnostic data collection was implemented."
        size="small"
      />
    </div>

    <div v-else class="diagnostic-content">
      <!-- Connection Status Section -->
      <div class="diagnostic-section">
        <h4 class="section-title">
          <NIcon class="section-icon"><WifiOutline /></NIcon>
          Connection Status
        </h4>
        <NDescriptions :column="2" size="small" label-placement="left" bordered>
          <NDescriptionsItem label="Status">
            <NTag 
              :type="getConnectionStatusType(diagnosticData.connectionStatus.connectionQuality)"
              size="small"
            >
              {{ diagnosticData.connectionStatus.connectionStatus }}
            </NTag>
          </NDescriptionsItem>
          <NDescriptionsItem label="Quality">
            <NTag 
              :type="getConnectionQualityType(diagnosticData.connectionStatus.connectionQuality)"
              size="small"
            >
              {{ formatConnectionQuality(diagnosticData.connectionStatus.connectionQuality) }}
            </NTag>
          </NDescriptionsItem>
          <NDescriptionsItem label="Transport">
            <NText code>{{ diagnosticData.connectionStatus.transportType }}</NText>
          </NDescriptionsItem>
          <NDescriptionsItem label="Socket ID">
            <NText v-if="diagnosticData.connectionStatus.socketId" code>
              {{ diagnosticData.connectionStatus.socketId }}
            </NText>
            <NText v-else depth="3">Not available</NText>
          </NDescriptionsItem>
          <NDescriptionsItem label="Reconnect Attempts">
            <NText>{{ diagnosticData.connectionStatus.reconnectAttempts }}</NText>
          </NDescriptionsItem>
          <NDescriptionsItem label="Last Disconnect">
            <NText v-if="diagnosticData.connectionStatus.lastDisconnectReason">
              {{ diagnosticData.connectionStatus.lastDisconnectReason }}
            </NText>
            <NText v-else depth="3">None</NText>
          </NDescriptionsItem>
        </NDescriptions>
      </div>

      <!-- Pinia Store Snapshot Section -->
      <div class="diagnostic-section">
        <h4 class="section-title">
          <NIcon class="section-icon"><DatabaseOutline /></NIcon>
          Application State Snapshot
          <NTag type="info" size="small" style="margin-left: 8px">
            {{ Object.keys(diagnosticData.piniaStoreSnapshot).length }} stores
          </NTag>
        </h4>
        
        <NCollapse>
          <NCollapseItem 
            v-for="(storeData, storeName) in diagnosticData.piniaStoreSnapshot" 
            :key="storeName"
            :title="getStoreDisplayName(storeName)"
            :name="storeName"
          >
            <template #header-extra>
              <NTag size="small" type="default">
                {{ getStoreDataSize(storeData) }}
              </NTag>
            </template>
            
            <div class="store-content">
              <NCode 
                :code="JSON.stringify(storeData, null, 2)" 
                language="json"
                show-line-numbers
                style="max-height: 300px; overflow-y: auto;"
              />
            </div>
          </NCollapseItem>
        </NCollapse>
      </div>

      <!-- Capture Information -->
      <div class="diagnostic-section">
        <h4 class="section-title">
          <NIcon class="section-icon"><TimeOutline /></NIcon>
          Capture Information
        </h4>
        <NDescriptions :column="1" size="small" label-placement="left" bordered>
          <NDescriptionsItem label="Captured At">
            {{ formatDate(diagnosticData.captureTimestamp) }}
          </NDescriptionsItem>
          <NDescriptionsItem label="Time Since Capture">
            {{ getTimeSinceCapture(diagnosticData.captureTimestamp) }}
          </NDescriptionsItem>
        </NDescriptions>
      </div>
    </div>
  </NCard>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import {
  NCard,
  NTag,
  NResult,
  NDescriptions,
  NDescriptionsItem,
  NText,
  NIcon,
  NCollapse,
  NCollapseItem,
  NCode
} from 'naive-ui';
import { WifiOutline, DatabaseOutline, TimeOutline } from '@vicons/ionicons5';
import { format, formatDistanceToNow } from 'date-fns';
import type { DiagnosticData } from '@/types/admin';

interface Props {
  diagnosticData?: DiagnosticData | null;
}

const props = defineProps<Props>();

// Helper functions
function getConnectionStatusType(quality: string): 'default' | 'primary' | 'info' | 'success' | 'warning' | 'error' {
  switch (quality) {
    case 'excellent': return 'success';
    case 'good': return 'info';
    case 'poor': return 'warning';
    case 'disconnected': return 'error';
    default: return 'default';
  }
}

function getConnectionQualityType(quality: string): 'default' | 'primary' | 'info' | 'success' | 'warning' | 'error' {
  return getConnectionStatusType(quality);
}

function formatConnectionQuality(quality: string): string {
  return quality.charAt(0).toUpperCase() + quality.slice(1);
}

function getStoreDisplayName(storeName: string): string {
  // Convert store names to more readable format
  const displayNames: Record<string, string> = {
    auth: 'Authentication',
    connection: 'Connection',
    theme: 'Theme Settings',
    chatStore: 'Chat',
    myOffersStore: 'My Offers',
    interestStore: 'Interests',
    notificationStore: 'Notifications',
    transactionStore: 'Transactions',
    adminDebug: 'Admin Debug'
  };
  
  return displayNames[storeName] || storeName;
}

function getStoreDataSize(storeData: any): string {
  try {
    const jsonString = JSON.stringify(storeData);
    const sizeInBytes = new Blob([jsonString]).size;
    
    if (sizeInBytes < 1024) {
      return `${sizeInBytes} B`;
    } else if (sizeInBytes < 1024 * 1024) {
      return `${(sizeInBytes / 1024).toFixed(1)} KB`;
    } else {
      return `${(sizeInBytes / (1024 * 1024)).toFixed(1)} MB`;
    }
  } catch {
    return 'Unknown size';
  }
}

function formatDate(dateString: string): string {
  try {
    return format(new Date(dateString), 'yyyy/MM/dd HH:mm:ss');
  } catch {
    return dateString;
  }
}

function getTimeSinceCapture(captureTime: string): string {
  try {
    return formatDistanceToNow(new Date(captureTime), { addSuffix: true });
  } catch {
    return 'Unknown';
  }
}
</script>

<style scoped>
.diagnostic-card {
  margin-bottom: 16px;
}

.no-diagnostic-data {
  text-align: center;
  padding: 20px;
}

.diagnostic-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.diagnostic-section {
  border: 1px solid var(--n-border-color);
  border-radius: 6px;
  padding: 16px;
  background: var(--n-card-color);
}

.section-title {
  display: flex;
  align-items: center;
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--n-text-color);
}

.section-icon {
  margin-right: 8px;
  color: var(--n-primary-color);
}

.store-content {
  margin-top: 8px;
}

/* Dark mode improvements */
:deep(.n-descriptions) {
  --n-th-color: var(--n-card-color);
  --n-td-color: var(--n-card-color);
}

:deep(.n-code) {
  background: var(--n-code-color);
  border-color: var(--n-border-color);
}

:deep(.n-collapse-item) {
  border-color: var(--n-border-color);
}

:deep(.n-collapse-item__header) {
  background: var(--n-card-color);
}

@media (max-width: 768px) {
  .diagnostic-section {
    padding: 12px;
  }
  
  :deep(.n-descriptions) {
    --n-descriptions-table-header-text-color: var(--n-text-color);
  }
}
</style>
